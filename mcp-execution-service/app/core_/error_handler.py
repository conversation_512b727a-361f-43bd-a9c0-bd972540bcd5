#!/usr/bin/env python3
"""
Error classification and handling utilities for MCP Executor Service.

This module provides functions to classify exceptions, determine retry behavior,
and format errors for Kafka propagation without exposing sensitive information.
"""

import json
import logging
import traceback
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Union

from .exceptions import (
    MCPExecutorError, ErrorCategory, ErrorCode,
    MCPConfigNotFoundError, MCPConfigInvalidError,
    ContainerCreationError, ContainerExecutionError,
    SSHConnectionError, MCPServerUnreachableError,
    MCPAuthenticationError, MCPToolExecutionError,
    PayloadValidationError, CredentialRetrievalError
)

logger = logging.getLogger(__name__)


def classify_exception(exception: Exception) -> Dict[str, Any]:
    """
    Classify an exception into error category and code.
    
    Args:
        exception: The exception to classify
        
    Returns:
        Dict containing error classification information
    """
    if isinstance(exception, MCPExecutorError):
        # Already classified custom exception
        return {
            "error_category": exception.error_category,
            "error_code": exception.error_code,
            "retryable": exception.retryable,
            "message": exception.message,
            "details": exception._sanitize_details()
        }
    
    # Classify standard Python exceptions
    exception_type = type(exception).__name__
    exception_message = str(exception)
    
    if isinstance(exception, json.JSONDecodeError):
        return {
            "error_category": ErrorCategory.KAFKA_ERROR,
            "error_code": ErrorCode.MESSAGE_DECODE_FAILED,
            "retryable": False,
            "message": f"Failed to decode JSON message: {exception_message}",
            "details": {"exception_type": exception_type}
        }
    
    elif isinstance(exception, (ConnectionError, TimeoutError)):
        return {
            "error_category": ErrorCategory.INFRASTRUCTURE_ERROR,
            "error_code": ErrorCode.NETWORK_ERROR,
            "retryable": True,
            "message": f"Network error: {exception_message}",
            "details": {"exception_type": exception_type}
        }
    
    elif isinstance(exception, ValueError):
        return {
            "error_category": ErrorCategory.VALIDATION_ERROR,
            "error_code": ErrorCode.INVALID_FIELD_TYPE,
            "retryable": False,
            "message": f"Value error: {exception_message}",
            "details": {"exception_type": exception_type}
        }
    
    elif isinstance(exception, KeyError):
        return {
            "error_category": ErrorCategory.VALIDATION_ERROR,
            "error_code": ErrorCode.MISSING_REQUIRED_FIELD,
            "retryable": False,
            "message": f"Missing required field: {exception_message}",
            "details": {"exception_type": exception_type}
        }
    
    else:
        # Unknown exception - classify as system error
        return {
            "error_category": ErrorCategory.SYSTEM_ERROR,
            "error_code": ErrorCode.INTERNAL_ERROR,
            "retryable": False,
            "message": f"Unexpected error: {exception_message}",
            "details": {"exception_type": exception_type}
        }


def is_retryable_error(exception: Exception) -> bool:
    """
    Determine if an error is retryable.
    
    Args:
        exception: The exception to check
        
    Returns:
        True if the error is retryable, False otherwise
    """
    classification = classify_exception(exception)
    return classification.get("retryable", False)


def extract_error_context(
    exception: Exception, 
    request_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Extract sanitized error context for Kafka propagation.
    
    Args:
        exception: The exception to extract context from
        request_id: Optional request ID for tracking
        correlation_id: Optional correlation ID for tracking
        additional_context: Optional additional context to include
        
    Returns:
        Sanitized error context dictionary
    """
    classification = classify_exception(exception)
    
    context = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "error_category": classification["error_category"].value,
        "error_code": classification["error_code"].value,
        "retryable": classification["retryable"],
        "message": classification["message"],
        "details": classification.get("details", {})
    }
    
    if request_id:
        context["request_id"] = request_id
    
    if correlation_id:
        context["correlation_id"] = correlation_id
    
    if additional_context:
        # Sanitize additional context
        sanitized_additional = _sanitize_context(additional_context)
        context["additional_context"] = sanitized_additional
    
    return context


def format_kafka_error_response(
    exception: Exception,
    request_id: Optional[str] = None,
    correlation_id: Optional[str] = None,
    additional_context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Format an exception into a Kafka-safe error response.
    
    This function ensures no stack traces or sensitive information
    are included in the Kafka message.
    
    Args:
        exception: The exception to format
        request_id: Optional request ID for tracking
        correlation_id: Optional correlation ID for tracking
        additional_context: Optional additional context to include
        
    Returns:
        Kafka-safe error response dictionary
    """
    # Log the full exception with stack trace for debugging
    logger.error(
        f"Error occurred - Request ID: {request_id}, "
        f"Correlation ID: {correlation_id}, "
        f"Exception: {exception}",
        exc_info=True
    )
    
    # Extract sanitized context for Kafka
    error_context = extract_error_context(
        exception, request_id, correlation_id, additional_context
    )
    
    # Format for Kafka response
    kafka_response = {
        "request_id": request_id,
        "error": error_context["message"],
        "error_type": error_context["error_category"],
        "error_code": error_context["error_code"],
        "retryable": error_context["retryable"],
        "timestamp": error_context["timestamp"],
        "mcp_status": "error"
    }
    
    # Add correlation ID if present
    if correlation_id:
        kafka_response["correlation_id"] = correlation_id
    
    # Add sanitized details if present
    if error_context.get("details"):
        kafka_response["details"] = error_context["details"]
    
    # Add additional context if present
    if error_context.get("additional_context"):
        kafka_response["additional_context"] = error_context["additional_context"]
    
    return kafka_response


def _sanitize_context(context: Dict[str, Any]) -> Dict[str, Any]:
    """
    Sanitize context dictionary to remove sensitive information.
    
    Args:
        context: Context dictionary to sanitize
        
    Returns:
        Sanitized context dictionary
    """
    sanitized = {}
    sensitive_keys = {
        'password', 'token', 'secret', 'key', 'credential', 
        'auth', 'authorization', 'bearer', 'api_key',
        'private_key', 'ssh_key', 'cert', 'certificate'
    }
    
    for key, value in context.items():
        key_lower = key.lower()
        
        # Check if key contains sensitive information
        if any(sensitive_word in key_lower for sensitive_word in sensitive_keys):
            sanitized[key] = "[REDACTED]"
        elif isinstance(value, str):
            # Truncate very long strings
            if len(value) > 500:
                sanitized[key] = value[:500] + "... [TRUNCATED]"
            else:
                sanitized[key] = value
        elif isinstance(value, dict):
            # Recursively sanitize nested dictionaries
            sanitized[key] = _sanitize_context(value)
        elif isinstance(value, list):
            # Sanitize lists (but limit size)
            if len(value) > 10:
                sanitized[key] = value[:10] + ["... [TRUNCATED]"]
            else:
                sanitized[key] = [
                    _sanitize_context(item) if isinstance(item, dict) else item
                    for item in value
                ]
        else:
            sanitized[key] = value
    
    return sanitized


def create_validation_error(field: str, reason: str, value: Any = None) -> PayloadValidationError:
    """
    Create a standardized validation error.
    
    Args:
        field: The field that failed validation
        reason: The reason for validation failure
        value: The invalid value (will be sanitized)
        
    Returns:
        PayloadValidationError instance
    """
    details = {"field": field}
    if value is not None:
        # Sanitize the value
        if isinstance(value, str) and len(value) > 100:
            details["value"] = value[:100] + "... [TRUNCATED]"
        else:
            details["value"] = str(value)
    
    return PayloadValidationError(field, reason, details)


def create_infrastructure_error(
    component: str, 
    operation: str, 
    reason: str, 
    retryable: bool = True
) -> MCPExecutorError:
    """
    Create a standardized infrastructure error.
    
    Args:
        component: The infrastructure component that failed
        operation: The operation that was being performed
        reason: The reason for failure
        retryable: Whether the error is retryable
        
    Returns:
        MCPExecutorError instance with infrastructure error category
    """
    message = f"{component} {operation} failed: {reason}"
    return MCPExecutorError(
        message,
        error_category=ErrorCategory.INFRASTRUCTURE_ERROR,
        error_code=ErrorCode.INTERNAL_ERROR,
        details={"component": component, "operation": operation},
        retryable=retryable
    )
