#!/usr/bin/env python3
"""
Test script for Phase 3 implementation: Service-Level Error Propagation.

Tests container client, SSH manager, and authentication manager error propagation.
"""

import pytest
import asyncio
from unittest.mock import MagicMock, patch, AsyncMock
import tempfile
import os

from app.services.container_client import ContainerManagementClient
from app.services.ssh_manager import SecureSSHKeyManager, GlobalSSHKeyManager
from app.services.authentication_manager import AuthenticationManager
from app.core_.exceptions import (
    ContainerCreationError, ContainerExecutionError,
    SSHConnectionError, MCPAuthenticationError
)


class TestContainerClientErrorPropagation:
    """Test container client error propagation."""
    
    @pytest.fixture
    def container_client(self):
        """Create container client for testing."""
        return ContainerManagementClient()
    
    @pytest.mark.asyncio
    async def test_create_container_with_exceptions_success(self, container_client):
        """Test successful container creation with exceptions enabled."""
        with patch.object(container_client, 'create_container') as mock_create:
            mock_create.return_value = (True, "Success", "container_123")
            
            container_id = await container_client.create_container_with_exceptions(
                "mcp_test", "user_test"
            )
            
            assert container_id == "container_123"
            mock_create.assert_called_once_with(
                "mcp_test", "user_test", "stdio", None, raise_on_error=True
            )
    
    @pytest.mark.asyncio
    async def test_create_container_with_exceptions_failure(self, container_client):
        """Test container creation failure with exceptions enabled."""
        with patch.object(container_client, 'create_container') as mock_create:
            mock_create.side_effect = ContainerCreationError(
                "mcp_test", "user_test", "Docker daemon unavailable"
            )
            
            with pytest.raises(ContainerCreationError) as exc_info:
                await container_client.create_container_with_exceptions(
                    "mcp_test", "user_test"
                )
            
            assert "Docker daemon unavailable" in str(exc_info.value)
            assert exc_info.value.mcp_id == "mcp_test"
            assert exc_info.value.user_id == "user_test"
    
    @pytest.mark.asyncio
    async def test_stop_container_with_exceptions_success(self, container_client):
        """Test successful container stop with exceptions enabled."""
        with patch.object(container_client, 'stop_container') as mock_stop:
            mock_stop.return_value = (True, "Stopped successfully")
            
            await container_client.stop_container_with_exceptions("container_123")
            
            mock_stop.assert_called_once_with("container_123", raise_on_error=True)
    
    @pytest.mark.asyncio
    async def test_stop_container_with_exceptions_failure(self, container_client):
        """Test container stop failure with exceptions enabled."""
        with patch.object(container_client, 'stop_container') as mock_stop:
            mock_stop.side_effect = ContainerExecutionError(
                "container_123", "Container not found"
            )
            
            with pytest.raises(ContainerExecutionError) as exc_info:
                await container_client.stop_container_with_exceptions("container_123")
            
            assert "Container not found" in str(exc_info.value)
            assert exc_info.value.container_id == "container_123"


class TestSSHManagerErrorPropagation:
    """Test SSH manager error propagation."""
    
    @pytest.fixture
    def ssh_manager(self):
        """Create SSH manager for testing."""
        return SecureSSHKeyManager()
    
    def test_get_ssh_key_path_with_exceptions_success(self, ssh_manager):
        """Test successful SSH key path retrieval with exceptions enabled."""
        # Create a temporary file to simulate existing SSH key
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.pem') as f:
            f.write("test key content")
            temp_path = f.name
        
        try:
            key_path = ssh_manager.get_ssh_key_path_with_exceptions(temp_path)
            assert key_path == temp_path
        finally:
            os.unlink(temp_path)
    
    def test_get_ssh_key_path_with_exceptions_empty_config(self, ssh_manager):
        """Test SSH key path retrieval with empty config."""
        with pytest.raises(SSHConnectionError) as exc_info:
            ssh_manager.get_ssh_key_path_with_exceptions("")
        
        assert "SSH key configuration is empty" in str(exc_info.value)
        assert exc_info.value.error_category.value == "infrastructure_error"
        assert exc_info.value.error_code.value == "ssh_connection_failed"
    
    def test_get_ssh_key_path_with_exceptions_invalid_key(self, ssh_manager):
        """Test SSH key path retrieval with invalid key content."""
        with patch.object(ssh_manager, '_create_temp_key_file') as mock_create:
            mock_create.side_effect = Exception("Invalid key format")
            
            with pytest.raises(SSHConnectionError) as exc_info:
                ssh_manager.get_ssh_key_path_with_exceptions("invalid_key_content")
            
            assert "Failed to process SSH key" in str(exc_info.value)


class TestGlobalSSHManagerErrorPropagation:
    """Test global SSH manager error propagation."""
    
    @pytest.fixture
    def global_ssh_manager(self):
        """Create global SSH manager for testing."""
        # Reset the singleton for testing
        GlobalSSHKeyManager._instance = None
        GlobalSSHKeyManager._initialized = False
        return GlobalSSHKeyManager()
    
    def test_get_ssh_key_path_with_exceptions_not_initialized(self, global_ssh_manager):
        """Test SSH key path retrieval when not initialized."""
        with pytest.raises(SSHConnectionError) as exc_info:
            global_ssh_manager.get_ssh_key_path_with_exceptions()
        
        assert "SSH key not initialized or not available" in str(exc_info.value)
        assert exc_info.value.error_category.value == "infrastructure_error"
    
    def test_initialize_ssh_key_with_exceptions_failure(self, global_ssh_manager):
        """Test SSH key initialization failure."""
        with patch('builtins.open', side_effect=PermissionError("Access denied")):
            with pytest.raises(SSHConnectionError) as exc_info:
                global_ssh_manager.initialize_ssh_key_with_exceptions("test_key_content")
            
            assert "Failed to create global SSH key file" in str(exc_info.value)
            assert exc_info.value.error_category.value == "infrastructure_error"


class TestAuthenticationManagerErrorPropagation:
    """Test authentication manager error propagation."""
    
    @pytest.fixture
    def auth_manager(self):
        """Create authentication manager for testing."""
        return AuthenticationManager()
    
    @pytest.mark.asyncio
    async def test_get_headers_with_exceptions_success(self, auth_manager):
        """Test successful header retrieval with exceptions enabled."""
        auth_manager.add_bearer_token("test_token_123")
        
        headers = await auth_manager.get_headers_with_exceptions()
        
        assert "Authorization" in headers
        assert headers["Authorization"] == "Bearer test_token_123"
    
    @pytest.mark.asyncio
    async def test_get_headers_with_exceptions_config_not_found(self, auth_manager):
        """Test header retrieval with non-existent config."""
        with pytest.raises(MCPAuthenticationError) as exc_info:
            await auth_manager.get_headers_with_exceptions("non_existent_config")
        
        assert "Authentication config not found" in str(exc_info.value)
        assert exc_info.value.error_category.value == "authentication_error"
        assert exc_info.value.error_code.value == "authentication_failed"
    
    @pytest.mark.asyncio
    async def test_get_headers_with_exceptions_bearer_token_not_available(self, auth_manager):
        """Test header retrieval when bearer token is not available."""
        from app.schemas.authentication_manager import AuthenticationType, AuthenticationConfig
        
        # Add config without token
        config = AuthenticationConfig(auth_type=AuthenticationType.BEARER, token=None)
        auth_manager.add_authentication("test_config", config)
        
        with pytest.raises(MCPAuthenticationError) as exc_info:
            await auth_manager.get_headers_with_exceptions("test_config")
        
        assert "Bearer token not available" in str(exc_info.value)
    
    def test_validate_token_with_exceptions_success(self, auth_manager):
        """Test successful token validation with exceptions enabled."""
        valid_token = "valid_token_123456789"
        
        result = auth_manager.validate_token_with_exceptions(valid_token)
        
        assert result is True
    
    def test_validate_token_with_exceptions_failure(self, auth_manager):
        """Test token validation failure with exceptions enabled."""
        invalid_token = "short"
        
        with pytest.raises(MCPAuthenticationError) as exc_info:
            auth_manager.validate_token_with_exceptions(invalid_token)
        
        assert "Token validation failed" in str(exc_info.value)
        assert exc_info.value.error_category.value == "authentication_error"


class TestServiceErrorIntegration:
    """Test integration of service-level error propagation."""
    
    @pytest.mark.asyncio
    async def test_error_propagation_chain(self):
        """Test that errors propagate correctly through service chain."""
        container_client = ContainerManagementClient()
        
        # Mock a network error in container creation
        with patch('aiohttp.ClientSession.post') as mock_post:
            mock_post.side_effect = asyncio.TimeoutError("Network timeout")
            
            with pytest.raises(ContainerCreationError) as exc_info:
                await container_client.create_container(
                    "mcp_test", "user_test", raise_on_error=True
                )
            
            # Verify error details are properly categorized
            error = exc_info.value
            assert error.error_category.value == "infrastructure_error"
            assert error.error_code.value == "container_creation_failed"
            assert error.retryable is True
            assert "mcp_test" in error.message
            assert "user_test" in error.message
    
    def test_error_sanitization_in_services(self):
        """Test that sensitive information is sanitized in service errors."""
        ssh_manager = SecureSSHKeyManager()
        
        with pytest.raises(SSHConnectionError) as exc_info:
            ssh_manager.get_ssh_key_path_with_exceptions("")
        
        # Convert to Kafka error format to test sanitization
        kafka_error = exc_info.value.to_kafka_error()
        
        assert kafka_error["error_type"] == "infrastructure_error"
        assert kafka_error["error_code"] == "ssh_connection_failed"
        assert kafka_error["retryable"] is True
        assert "details" in kafka_error


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
