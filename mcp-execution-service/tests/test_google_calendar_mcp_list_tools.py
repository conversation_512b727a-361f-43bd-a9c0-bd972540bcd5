"""
Test for listing tools from deployed Google Calendar MCP server.

This test connects to the deployed Google Calendar MCP server and lists available tools
using the MCP executor service client.
"""

import asyncio
import logging
import pytest
from typing import List, Dict, Any

from app.core_.client import MC<PERSON>lient, create_legacy_client
import mcp.types as types

# Configure logging for tests
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",
)
logger = logging.getLogger(__name__)


class GoogleCalendarMCPTester:
    """Test class for Google Calendar MCP server functionality."""

    def __init__(self, server_url: str):
        """
        Initialize the tester with the Google Calendar MCP server URL.

        Args:
            server_url: The URL of the deployed Google Calendar MCP server
        """
        self.server_url = server_url
        self.logger = logging.getLogger(f"{__name__}.GoogleCalendarMCPTester")

    async def test_connection_and_list_tools(self) -> Dict[str, Any]:
        """
        Test connection to Google Calendar MCP server and list available tools.

        Returns:
            Dict containing test results and tool information
        """
        test_result = {
            "success": False,
            "error": None,
            "tools": [],
            "tool_count": 0,
            "connection_info": {},
            "server_url": self.server_url,
        }

        try:
            self.logger.info(
                f"🔗 Connecting to Google Calendar MCP server: {self.server_url}"
            )

            # Create MCP client for HTTP connection
            # The server URL should support SSE or streamable HTTP
            client = create_legacy_client(server_url=self.server_url)

            async with client as connected_client:
                self.logger.info(
                    "✅ Successfully connected to Google Calendar MCP server!"
                )

                # Get connection information
                connection_info = connected_client.get_connection_info()
                test_result["connection_info"] = connection_info
                self.logger.info(f"📊 Connection info: {connection_info}")

                # List available tools
                self.logger.info("🔍 Listing available tools...")
                tools = await connected_client.list_tools()

                test_result["tools"] = [
                    {
                        "name": tool.name,
                        "description": tool.description,
                        "input_schema": (
                            tool.inputSchema.model_dump()
                            if hasattr(tool.inputSchema, "model_dump")
                            else str(tool.inputSchema)
                        ),
                    }
                    for tool in tools
                ]
                test_result["tool_count"] = len(tools)
                test_result["success"] = True

                self.logger.info(f"🎉 Successfully retrieved {len(tools)} tools!")

                # Log tool details
                if tools:
                    self.logger.info("📋 Available Google Calendar MCP tools:")
                    for i, tool in enumerate(tools, 1):
                        self.logger.info(f"   {i}. {tool.name}")
                        self.logger.info(f"      Description: {tool.description}")
                        if hasattr(tool, "inputSchema") and tool.inputSchema:
                            # Log schema summary
                            schema_info = "Has input schema"
                            if hasattr(tool.inputSchema, "properties"):
                                prop_count = (
                                    len(tool.inputSchema.properties)
                                    if tool.inputSchema.properties
                                    else 0
                                )
                                schema_info = f"Schema with {prop_count} properties"
                            self.logger.info(f"      Schema: {schema_info}")
                        self.logger.info("")
                else:
                    self.logger.warning("⚠️ No tools found on the server")

                return test_result

        except Exception as e:
            error_msg = f"Failed to connect or list tools: {e}"
            self.logger.error(f"❌ {error_msg}")
            test_result["error"] = str(e)
            test_result["success"] = False
            return test_result

    async def test_list_resources(self) -> Dict[str, Any]:
        """
        Test listing resources from Google Calendar MCP server.

        Returns:
            Dict containing resource information
        """
        test_result = {
            "success": False,
            "error": None,
            "resources": [],
            "resource_count": 0,
        }

        try:
            self.logger.info("🔍 Testing resource listing...")

            client = create_legacy_client(server_url=self.server_url)

            async with client as connected_client:
                # List available resources
                resources = await connected_client.list_resources()

                test_result["resources"] = [
                    {
                        "uri": resource.uri,
                        "name": resource.name,
                        "description": resource.description,
                        "mimeType": (
                            resource.mimeType if hasattr(resource, "mimeType") else None
                        ),
                    }
                    for resource in resources
                ]
                test_result["resource_count"] = len(resources)
                test_result["success"] = True

                self.logger.info(f"📚 Found {len(resources)} resources")

                if resources:
                    for i, resource in enumerate(resources, 1):
                        self.logger.info(f"   {i}. {resource.uri} - {resource.name}")

                return test_result

        except Exception as e:
            error_msg = f"Failed to list resources: {e}"
            self.logger.error(f"❌ {error_msg}")
            test_result["error"] = str(e)
            return test_result

    async def test_list_prompts(self) -> Dict[str, Any]:
        """
        Test listing prompts from Google Calendar MCP server.

        Returns:
            Dict containing prompt information
        """
        test_result = {
            "success": False,
            "error": None,
            "prompts": [],
            "prompt_count": 0,
        }

        try:
            self.logger.info("🔍 Testing prompt listing...")

            client = create_legacy_client(server_url=self.server_url)

            async with client as connected_client:
                # List available prompts
                prompts = await connected_client.list_prompts()

                test_result["prompts"] = [
                    {
                        "name": prompt.name,
                        "description": prompt.description,
                        "arguments": (
                            prompt.arguments if hasattr(prompt, "arguments") else []
                        ),
                    }
                    for prompt in prompts
                ]
                test_result["prompt_count"] = len(prompts)
                test_result["success"] = True

                self.logger.info(f"💬 Found {len(prompts)} prompts")

                if prompts:
                    for i, prompt in enumerate(prompts, 1):
                        self.logger.info(
                            f"   {i}. {prompt.name} - {prompt.description}"
                        )

                return test_result

        except Exception as e:
            error_msg = f"Failed to list prompts: {e}"
            self.logger.error(f"❌ {error_msg}")
            test_result["error"] = str(e)
            return test_result

    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """
        Run comprehensive test of Google Calendar MCP server.

        Returns:
            Dict containing all test results
        """
        self.logger.info("🚀 Starting comprehensive Google Calendar MCP server test...")

        comprehensive_result = {
            "server_url": self.server_url,
            "overall_success": False,
            "tests": {},
        }

        # Test 1: Connection and tool listing
        self.logger.info("=" * 60)
        self.logger.info("TEST 1: Connection and Tool Listing")
        self.logger.info("=" * 60)
        tools_result = await self.test_connection_and_list_tools()
        comprehensive_result["tests"]["tools"] = tools_result

        # Test 2: Resource listing
        self.logger.info("=" * 60)
        self.logger.info("TEST 2: Resource Listing")
        self.logger.info("=" * 60)
        resources_result = await self.test_list_resources()
        comprehensive_result["tests"]["resources"] = resources_result

        # Test 3: Prompt listing
        self.logger.info("=" * 60)
        self.logger.info("TEST 3: Prompt Listing")
        self.logger.info("=" * 60)
        prompts_result = await self.test_list_prompts()
        comprehensive_result["tests"]["prompts"] = prompts_result

        # Determine overall success
        all_tests_passed = all(
            result["success"] for result in comprehensive_result["tests"].values()
        )
        comprehensive_result["overall_success"] = all_tests_passed

        # Summary
        self.logger.info("=" * 60)
        self.logger.info("TEST SUMMARY")
        self.logger.info("=" * 60)

        for test_name, result in comprehensive_result["tests"].items():
            status = "✅ PASSED" if result["success"] else "❌ FAILED"
            self.logger.info(f"{test_name.upper()}: {status}")
            if not result["success"] and result.get("error"):
                self.logger.info(f"  Error: {result['error']}")

        self.logger.info("=" * 60)
        overall_status = (
            "✅ ALL TESTS PASSED" if all_tests_passed else "❌ SOME TESTS FAILED"
        )
        self.logger.info(f"OVERALL: {overall_status}")
        self.logger.info("=" * 60)

        return comprehensive_result


# Pytest integration
@pytest.mark.asyncio
async def test_google_calendar_mcp_tools():
    """Pytest entry point for Google Calendar MCP tools test."""
    server_url = "https://google-calendar-mcp-dev-624209391722.us-central1.run.app"

    tester = GoogleCalendarMCPTester(server_url)
    result = await tester.test_connection_and_list_tools()

    # Assert the test passed
    assert result[
        "success"
    ], f"Google Calendar MCP tools test failed: {result.get('error', 'Unknown error')}"

    # Assert we got some tools
    assert result["tool_count"] > 0, "No tools found on Google Calendar MCP server"

    # Log the tools for verification
    logger.info(f"Found {result['tool_count']} tools on Google Calendar MCP server")
    for tool in result["tools"]:
        logger.info(f"  - {tool['name']}: {tool['description']}")


@pytest.mark.asyncio
async def test_google_calendar_mcp_comprehensive():
    """Pytest entry point for comprehensive Google Calendar MCP test."""
    server_url = "https://google-calendar-mcp-dev-624209391722.us-central1.run.app"

    tester = GoogleCalendarMCPTester(server_url)
    result = await tester.run_comprehensive_test()

    # Assert overall success
    assert result["overall_success"], "Google Calendar MCP comprehensive test failed"


# Main entry point for direct execution
async def main():
    """Main entry point for running Google Calendar MCP tests."""
    server_url = "https://google-calendar-mcp-dev-624209391722.us-central1.run.app"

    tester = GoogleCalendarMCPTester(server_url)
    result = await tester.run_comprehensive_test()

    # Exit with appropriate code
    exit_code = 0 if result["overall_success"] else 1
    logger.info(f"Exiting with code: {exit_code}")
    return exit_code


if __name__ == "__main__":
    logger.info("Starting Google Calendar MCP server tests...")
    exit_code = asyncio.run(main())
    exit(exit_code)
