#!/usr/bin/env python3
"""
Test script for simplified container flow.
Tests the complete container lifecycle: create → fetch command → execute → stop → delete
"""

import asyncio
import logging
import pytest
from unittest.mock import AsyncMock, patch, MagicMock

from app.core_.mcp_executor import MC<PERSON>Executor
from app.services.container_client import ContainerManagementClient
from app.core_.client import MC<PERSON>lient
from app.services.ssh_manager import initialize_global_ssh_key, get_global_ssh_manager
from app.config.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class TestSimplifiedContainerFlow:
    """Test class for simplified container execution flow."""

    @pytest.fixture
    def mock_producer(self):
        """Create a mock Kafka producer."""
        producer = AsyncMock()
        producer.send = AsyncMock()
        return producer

    @pytest.fixture
    def executor(self, mock_producer):
        """Create a test executor instance."""
        return MCPExecutor(mock_producer)

    @pytest.fixture
    def container_client(self):
        """Create a test container client instance."""
        return ContainerManagementClient()

    @pytest.mark.asyncio
    async def test_container_lifecycle_flow(self, executor):
        """Test complete container lifecycle: create → fetch command → execute → stop → delete"""

        # Mock the container client methods
        with patch.object(
            ContainerManagementClient, "create_container"
        ) as mock_create, patch.object(
            ContainerManagementClient, "stop_container"
        ) as mock_stop, patch.object(
            ContainerManagementClient, "delete_container"
        ) as mock_delete:

            # Setup mocks
            mock_create.return_value = (
                True,
                "Container created successfully",
                "test-container-id",
            )
            mock_stop.return_value = (True, "Container stopped successfully")
            mock_delete.return_value = (True, "Container deleted successfully")

            # Mock the MCP client execution
            with patch.object(MCPClient, "__aenter__") as mock_enter, patch.object(
                MCPClient, "__aexit__"
            ) as mock_exit, patch.object(
                MCPClient, "call_tool"
            ) as mock_call_tool, patch.object(
                MCPClient, "_get_container_command"
            ) as mock_get_command:

                # Setup mocks
                mock_client_instance = AsyncMock()
                mock_client_instance.call_tool = AsyncMock(
                    return_value={"result": "test_success"}
                )
                mock_enter.return_value = mock_client_instance
                mock_exit.return_value = None
                mock_call_tool.return_value = {"result": "test_success"}
                mock_get_command.return_value = "python server.py"

                # Execute container tool
                result = await executor._execute_container_tool(
                    tool_name="test_tool",
                    tool_parameters={"param1": "value1"},
                    user_id="test-user",
                    mcp_id="test-mcp",
                )

                # Verify the flow
                assert result is not None
                assert len(result) > 0

                # Verify container lifecycle calls
                mock_create.assert_called_once_with(
                    mcp_id="test-mcp",
                    user_id="test-user",
                    container_type="stdio",
                    env=None,
                )
                mock_stop.assert_called_once_with("test-container-id")
                mock_delete.assert_called_once_with("test-container-id")

                # Verify MCP client calls
                mock_enter.assert_called_once()
                mock_client_instance.call_tool.assert_called_once_with(
                    "test_tool", {"param1": "value1"}
                )

    @pytest.mark.asyncio
    async def test_container_command_detection_cmd_only(self):
        """Test command detection with Cmd-only containers."""

        # Mock subprocess for docker inspect
        with patch("subprocess.run") as mock_run:
            # Mock successful Cmd detection
            mock_result = MagicMock()
            mock_result.returncode = 0
            mock_result.stdout = '["node", "dist/index.js"]'
            mock_run.return_value = mock_result

            # Mock global SSH manager
            with patch(
                "app.services.ssh_manager.get_global_ssh_manager"
            ) as mock_ssh_manager:
                mock_manager = MagicMock()
                mock_manager.get_ssh_key_path.return_value = "/path/to/ssh/key"
                mock_ssh_manager.return_value = mock_manager

                # Create MCP client and test command detection
                client = MCPClient(
                    docker_image="test-container",
                    connection_type="ssh_docker",
                    container_command=None,
                )

                command = await client._get_container_command()

                # Verify command was detected correctly
                assert command == "node dist/index.js"

                # Verify only one SSH call was made (Cmd detection succeeded)
                assert mock_run.call_count == 1

                # Verify the correct docker inspect command was called
                call_args = mock_run.call_args[0][0]
                assert "docker inspect" in " ".join(call_args)
                assert "--format='{{json .Config.Cmd}}'" in " ".join(call_args)

    @pytest.mark.asyncio
    async def test_container_command_detection_entrypoint_fallback(self):
        """Test command detection fallback to Entrypoint + Cmd."""

        # Mock subprocess for docker inspect
        with patch("subprocess.run") as mock_run:
            # Mock failed Cmd detection, successful Entrypoint + Cmd detection
            def side_effect(*args, **kwargs):
                # Accept kwargs to match subprocess.run signature
                _ = kwargs  # Suppress unused warning
                call_args = args[0]
                if "--format='{{json .Config.Cmd}}'" in " ".join(call_args):
                    # First call (Cmd only) returns null
                    result = MagicMock()
                    result.returncode = 0
                    result.stdout = "null"
                    return result
                elif (
                    "--format='{{json .Config.Entrypoint}} {{json .Config.Cmd}}'"
                    in " ".join(call_args)
                ):
                    # Second call (Entrypoint + Cmd) returns valid data
                    result = MagicMock()
                    result.returncode = 0
                    result.stdout = '["python"] ["server.py"]'
                    return result
                else:
                    result = MagicMock()
                    result.returncode = 1
                    return result

            mock_run.side_effect = side_effect

            # Mock global SSH manager
            with patch(
                "app.services.ssh_manager.get_global_ssh_manager"
            ) as mock_ssh_manager:
                mock_manager = MagicMock()
                mock_manager.get_ssh_key_path.return_value = "/path/to/ssh/key"
                mock_ssh_manager.return_value = mock_manager

                # Create MCP client and test command detection
                client = MCPClient(
                    docker_image="test-container",
                    connection_type="ssh_docker",
                    container_command=None,
                )

                command = await client._get_container_command()

                # Verify command was detected correctly from entrypoint + cmd
                assert command == "python server.py"

                # Verify two SSH calls were made (Cmd failed, Entrypoint+Cmd succeeded)
                assert mock_run.call_count == 2

    @pytest.mark.asyncio
    async def test_container_command_detection_final_fallback(self):
        """Test command detection final fallback to default."""

        # Mock subprocess for docker inspect
        with patch("subprocess.run") as mock_run:
            # Mock both detection methods failing
            mock_result = MagicMock()
            mock_result.returncode = 1  # Failure
            mock_run.return_value = mock_result

            # Mock global SSH manager
            with patch(
                "app.services.ssh_manager.get_global_ssh_manager"
            ) as mock_ssh_manager:
                mock_manager = MagicMock()
                mock_manager.get_ssh_key_path.return_value = "/path/to/ssh/key"
                mock_ssh_manager.return_value = mock_manager

                # Create MCP client and test command detection
                client = MCPClient(
                    docker_image="test-container",
                    connection_type="ssh_docker",
                    container_command=None,
                )

                command = await client._get_container_command()

                # Verify default command was used
                assert command == "python server.py"

                # Verify both SSH calls were attempted
                assert mock_run.call_count == 2

    @pytest.mark.asyncio
    async def test_global_ssh_key_usage(self):
        """Test that global SSH key is used correctly."""

        # Initialize global SSH key if available
        if settings.ssh_key_content:
            initialize_global_ssh_key(settings.ssh_key_content)

            # Get global SSH manager
            manager = get_global_ssh_manager()
            key_path = manager.get_ssh_key_path()

            if key_path:
                logger.info(f"✅ Global SSH key available: {key_path}")

                # Test that MCP client uses global SSH key
                with patch("subprocess.run") as mock_run:
                    mock_result = MagicMock()
                    mock_result.returncode = 0
                    mock_result.stdout = '["python", "server.py"]'
                    mock_run.return_value = mock_result

                    client = MCPClient(
                        docker_image="test-container",
                        connection_type="ssh_docker",
                        container_command=None,
                    )

                    await client._get_container_command()

                    # Verify SSH command used global key
                    call_args = mock_run.call_args[0][0]
                    assert key_path in call_args
                    logger.info("✅ Global SSH key used in command detection")
            else:
                logger.warning("⚠️ Global SSH key not available for testing")
        else:
            logger.warning("⚠️ No SSH key content in settings for testing")

    @pytest.mark.asyncio
    async def test_container_creation_failure(self, executor):
        """Test error handling when container creation fails."""

        with patch.object(ContainerManagementClient, "create_container") as mock_create:
            # Mock container creation failure
            mock_create.return_value = (False, "Container creation failed", None)

            # Test that exception is raised
            with pytest.raises(Exception, match="Failed to create container"):
                await executor._execute_container_tool(
                    tool_name="test_tool",
                    tool_parameters={"param1": "value1"},
                    user_id="test-user",
                    mcp_id="test-mcp",
                )

    @pytest.mark.asyncio
    async def test_container_command_detection_error_handling(self):
        """Test error handling in command detection."""

        # Mock subprocess to raise exception
        with patch("subprocess.run") as mock_run:
            mock_run.side_effect = Exception("SSH connection failed")

            # Mock global SSH manager
            with patch(
                "app.services.ssh_manager.get_global_ssh_manager"
            ) as mock_ssh_manager:
                mock_manager = MagicMock()
                mock_manager.get_ssh_key_path.return_value = "/path/to/ssh/key"
                mock_ssh_manager.return_value = mock_manager

                # Create MCP client and test command detection
                client = MCPClient(
                    docker_image="test-container",
                    connection_type="ssh_docker",
                    container_command=None,
                )

                command = await client._get_container_command()

                # Verify default command was used despite error
                assert command == "python server.py"


if __name__ == "__main__":
    # Run tests manually for debugging
    asyncio.run(TestSimplifiedContainerFlow().test_global_ssh_key_usage())
